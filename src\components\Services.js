"use client";

import { useRef, useState, useEffect } from 'react';
import SectionTitleAnimation from './SectionTitleAnimation';

// ServiceCard component with scroll-triggered animation (using exact AnimatedTitle logic)
const ServiceCard = ({ title, description, features, visual }) => {
  const cardRef = useRef(null);
  const [hasAnimatedIn, setHasAnimatedIn] = useState(false);
  const [shouldShow, setShouldShow] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (!cardRef.current) return;

      const rect = cardRef.current.getBoundingClientRect();
      const windowHeight = window.innerHeight;

      // Element's top position relative to viewport
      const elementTop = rect.top;

      // Trigger point: configurable position in viewport
      const triggerPoint = windowHeight * 0.75;

      // Show animation when scrolling down and element comes into view
      if (elementTop <= triggerPoint && !hasAnimatedIn) {
        setHasAnimatedIn(true);
        setShouldShow(true);
      }

      // Hide animation when scrolling back up past the same trigger point
      if (elementTop > triggerPoint && hasAnimatedIn) {
        setHasAnimatedIn(false);
        setShouldShow(false);
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    // Check initial position
    handleScroll();

    return () => window.removeEventListener('scroll', handleScroll);
  }, [hasAnimatedIn]);

  const content = (
    <div className="space-y-4">
      <h3 className="font-heading font-bold text-2xl text-secondary">
        {title}
      </h3>
      <p className="text-md text-secondary leading-relaxed">
        {description}
      </p>
      <ul className="space-y-2 text-secondary">
        {features.map((feature, idx) => (
          <li key={idx} className="flex items-center">
            <span className="w-2 h-2 rounded-full mr-2"></span>
            {feature}
          </li>
        ))}
      </ul>
    </div>
  );

  const visualElement = (
    <div className="flex items-center justify-center bg-gray-100 rounded-lg p-8 min-h-[250px]">
      {visual}
    </div>
  );

  return (
    <div
      ref={cardRef}
      className="mx-auto mb-12"
      style={{
        opacity: shouldShow ? 1 : 0,
        transform: shouldShow ? 'scale(1)' : 'scale(0.9)',
        transition: 'opacity 0.3s ease-out, transform 0.3s ease-out'
      }}
    >
      <div className="flex gap-8 items-center">
        {/* Visual element - 2/3 of the container */}
        <div className="w-2/3">
          {visualElement}
        </div>
        {/* Text content - 1/3 of the container */}
        <div className="w-1/3">
          {content}
        </div>
      </div>
    </div>
  );
};

// Multiple services for expanded layout
const services = [
  {
    title: "Brand Identity Design",
    description: "Craft a cohesive brand identity that resonates with your audience. From logos to brand guidelines, I create visual systems that tell your story and ensure consistency across all platforms.",
    features: [
      "Logo Design",
      "Brand Guidelines",
      "Color Palette & Typography",
      "Business Card & Stationery Design"
    ],
    visual: (
      <div className="text-center p-4">
        <div className="w-24 h-24 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full mx-auto mb-3 flex items-center justify-center shadow-lg">
          <span className="text-white text-2xl font-bold">B</span>
        </div>
        <p className="text-secondary/70 text-sm">Visual for Brand Identity</p>
      </div>
    )
  },
  {
    title: "Marketing & Promotional Design",
    description: "Design eye-catching visuals for both digital and print campaigns. I create banners, posters, newsletters, and assets that help your brand stand out in a competitive market.",
    features: [
      "Social Media & Web Banners",
      "Print Materials (Posters, Flyers, Business Cards)",
      "Newsletter & Email Design",
      "Promotional Campaign Graphics"
    ],
    visual: (
      <div className="text-center p-4">
        <div className="w-24 h-24 bg-gradient-to-br from-green-400 to-blue-500 rounded-lg mx-auto mb-3 flex items-center justify-center shadow-lg">
          <span className="text-white text-2xl font-bold">M</span>
        </div>
        <p className="text-secondary/70 text-sm">Visual for Marketing Design</p>
      </div>
    )
  },
  {
    title: "Motion Design & 2D Animation",
    description: "Bring your message to life with smooth, engaging 2D animations. I design motion graphics perfect for commercials, product showcases, and digital campaigns.",
    features: [
      "Commercial 2D Animations (After Effects)",
      "Animated Social Ads & Explainers",
      "Logo Animations & Motion Branding"
    ],
    visual: (
      <div className="text-center p-4">
        <div className="w-24 h-24 bg-gradient-to-br from-orange-400 to-red-500 rounded-lg mx-auto mb-3 flex items-center justify-center shadow-lg">
          <span className="text-white text-2xl font-bold">A</span>
        </div>
        <p className="text-secondary/70 text-sm">Visual for Motion Design</p>
      </div>
    )
  },
  {
    title: "Web & Landing Page Design",
    description: "Design clean, user-friendly landing pages and websites. I offer simple, modern styling with the option to develop fully functional AI-assisted web prototypes.",
    features: [
      "Landing Page Design",
      "Website Styling & Mockups",
      "AI-Powered Web Prototyping (Optional Development Support)"
    ],
    visual: (
      <div className="text-center p-4">
        <div className="w-24 h-24 bg-gradient-to-br from-cyan-400 to-blue-600 rounded-lg mx-auto mb-3 flex items-center justify-center shadow-lg">
          <span className="text-white text-2xl font-bold">W</span>
        </div>
        <p className="text-secondary/70 text-sm">Visual for Web Design</p>
      </div>
    )
  },
  {
    title: "3D Rendering & Visual Support",
    description: "I use 3D rendering as a creative tool to elevate visual projects. Whether for banners, product showcases, or promotional materials, 3D visuals can make your designs stand out.",
    features: [
      "3D Renderings for Static Graphics",
      "Product Visualizations",
      "Integrated 3D Elements for Branding & Marketing"
    ],
    visual: (
      <div className="text-center p-4">
        <div className="w-24 h-24 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg mx-auto mb-3 flex items-center justify-center shadow-lg">
          <span className="text-white text-2xl font-bold">3D</span>
        </div>
        <p className="text-secondary/70 text-sm">Visual for 3D Rendering</p>
      </div>
    )
  }
];

const Services = () => {
  const sectionRef = useRef(null);

  return (
    <>
      {/* Reusable Section Title Animation */}
      <SectionTitleAnimation
        title="Services"
        currentSectionRef={sectionRef}
        previousSectionSelector='[data-section="home"]'
        zIndex="z-0"
        className="font-heading font-extrabold text-secondary text-4xl lg:text-6xl"
      />

      {/* Services Section - provides scroll space and natural service card layout */}
      <section
        ref={sectionRef}
        data-section="services"
        className="min-h-[300vh] relative z-10"
      >
        <div className="max-w-screen-xl w-full mx-auto px-6">
          {/* Spacer to push services down initially so they appear from bottom - NO BACKGROUND */}
          <div className="h-[100vh]"></div>

          {/* Service Cards Container with background to cover title */}
          <div className=" relative z-10">
            {/* Service Cards - positioned naturally in the document flow */}
            {services.map((service, index) => (
              <ServiceCard
                key={index}
                title={service.title}
                description={service.description}
                features={service.features}
                visual={service.visual}
              />
            ))}
          </div>

        </div>
      </section>
    </>
  );
};

export default Services;
